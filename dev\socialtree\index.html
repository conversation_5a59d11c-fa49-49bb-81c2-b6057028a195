
<!DOCTYPE html>
<html lang="en" oncontextmenu="return false">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="openweather-api-key" content="********************************">

    <title>Yusuf - Digital Wizard</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">

    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#6366f1',
                        secondary: '#8b5cf6'
                    },
                    borderRadius: {
                        'none': '0px',
                        'sm': '4px',
                        DEFAULT: '8px',
                        'md': '12px',
                        'lg': '16px',
                        'xl': '20px',
                        '2xl': '24px',
                        '3xl': '32px',
                        'full': '9999px',
                        'button': '8px'
                    },
                    fontFamily: {
                        'sans': ['Inter', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <style>
        :where([class^="ri-"])::before {
            content: "\f3c2";
        }

        body {
            font-family: 'Inter', sans-serif;
            background: #0b1220; /* placeholder dark blank bg; replaced by Liquid Ether */
            min-height: 100vh;
            overflow-x: hidden;
        }

        .glass-morphism {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .glass-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .profile-orb {
            position: relative;
            animation: orbPulse 5s ease-in-out infinite;
        }

        .profile-orb::before {
            content: '';
            position: absolute;
            top: -4px;
            left: -4px;
            right: -4px;
            bottom: -4px;
            border-radius: 50%;
            background: linear-gradient(45deg, #6366f1, #8b5cf6, #06b6d4, #10b981);
            background-size: 400% 400%;
            animation: gradientShift 10s ease infinite;
            opacity: 0.5;
            z-index: -1;
        }

        /* Animated gradient ring around avatar */
        .profile-orb::after {
            content: '';
            position: absolute;
            top: -6px; left: -6px; right: -6px; bottom: -6px;
            border-radius: 50%;
            background: conic-gradient(#6366f1, #8b5cf6, #06b6d4, #10b981, #6366f1);
            animation: spinRing 8s linear infinite;
            -webkit-mask: radial-gradient(farthest-side, transparent calc(100% - 6px), #000 0);
            mask: radial-gradient(farthest-side, transparent calc(100% - 6px), #000 0);
            opacity: 0.9;
        }
        @keyframes spinRing { to { transform: rotate(360deg); } }


        @keyframes orbPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.02); }
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .social-button {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .social-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s;
        }

        .social-button:hover::before {
            left: 100%;
        }

        .social-button:hover {
            transform: translateY(-2px) scale(1.02);
            background: rgba(255, 255, 255, 0.15);
        }

        .social-button:active {
            transform: translateY(0) scale(0.98);
        }

        .instagram-glow:hover {
            box-shadow: 0 0 30px rgba(225, 48, 108, 0.5), 0 0 60px rgba(225, 48, 108, 0.3);
            border-color: rgba(225, 48, 108, 0.8);
        }

        .twitter-glow:hover {
            box-shadow: 0 0 30px rgba(29, 161, 242, 0.5), 0 0 60px rgba(29, 161, 242, 0.3);
            border-color: rgba(29, 161, 242, 0.8);
        }

        .linkedin-glow:hover {
            box-shadow: 0 0 30px rgba(10, 102, 194, 0.5), 0 0 60px rgba(10, 102, 194, 0.3);
            border-color: rgba(10, 102, 194, 0.8);
        }

        .github-glow:hover {
            box-shadow: 0 0 30px rgba(255, 255, 255, 0.5), 0 0 60px rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.8);
        }

        .youtube-glow:hover {
            box-shadow: 0 0 30px rgba(255, 0, 0, 0.5), 0 0 60px rgba(255, 0, 0, 0.3);
            border-color: rgba(255, 0, 0, 0.8);
        }

        .tiktok-glow:hover {
            box-shadow: 0 0 30px rgba(0, 255, 255, 0.5), 0 0 60px rgba(0, 255, 255, 0.3);
            border-color: rgba(0, 255, 255, 0.8);
        }

        .discord-glow:hover {
            box-shadow: 0 0 30px rgba(88, 101, 242, 0.5), 0 0 60px rgba(88, 101, 242, 0.3);
            border-color: rgba(88, 101, 242, 0.8);
        }

        .twitch-glow:hover {
            box-shadow: 0 0 30px rgba(145, 70, 255, 0.5), 0 0 60px rgba(145, 70, 255, 0.3);
            border-color: rgba(145, 70, 255, 0.8);
        }

        .fade-in {
            opacity: 0;
            transform: translateY(30px);
            animation: fadeInUp 0.8s ease-out forwards;
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }


        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0; }
            50% { transform: translateY(-100px) rotate(180deg); opacity: 1; }
        }
        /* Added glow effects for new platforms */
        .snapchat-glow:hover {
            box-shadow: 0 0 30px rgba(255, 252, 0, 0.5), 0 0 60px rgba(255, 252, 0, 0.3);
            border-color: rgba(255, 252, 0, 0.8);
        }
        .spotify-glow:hover {
            box-shadow: 0 0 30px rgba(29, 185, 84, 0.5), 0 0 60px rgba(29, 185, 84, 0.3);
            border-color: rgba(29, 185, 84, 0.8);
        }
        .portfolio-glow:hover {
            box-shadow: 0 0 30px rgba(255, 165, 0, 0.5), 0 0 60px rgba(255, 165, 0, 0.3);
            border-color: rgba(255, 165, 0, 0.8);
        }
        .contact-glow:hover {
            box-shadow: 0 0 30px rgba(255, 0, 0, 0.5), 0 0 60px rgba(255, 0, 0, 0.3);
            border-color: rgba(255, 0, 0, 0.8);
        }

        /* Contact modal solid background */
        #contact-modal .glass-card { background: #0f172a; border: 1px solid rgba(255,255,255,0.12); }
        /* Send success animation */
        @keyframes sendSuccess {
            0% { transform: scale(1); box-shadow: 0 0 0 rgba(34,197,94,0); }
            50% { transform: scale(1.03); box-shadow: 0 0 24px rgba(34,197,94,0.45); }
            100% { transform: scale(1); box-shadow: 0 0 0 rgba(34,197,94,0); }
        }
        .submit-btn.success { animation: sendSuccess 0.8s ease; background: #16a34a !important; }
        /* Clock + Weather HUD */
        .hud-badge { background: rgba(255,255,255,0.06); border: 1px solid rgba(255,255,255,0.15); }
        .bubble-card { background: rgba(15, 23, 42, 0.75); border: 1px solid rgba(255,255,255,0.12); backdrop-filter: none; }
        #weather-bubble { opacity: 0; transform: translateY(-6px) scale(0.98); transition: opacity .18s ease, transform .18s ease; transform-origin: top right; pointer-events: none; }
        #weather-bubble.open { opacity: 1; transform: translateY(0) scale(1); pointer-events: auto; }
        /* Weather icons */
        .wx-icon { width: 28px; height: 28px; display: inline-block; }
        @keyframes spinSlow { from { transform: rotate(0deg); } to { transform: rotate(360deg); } }
        @keyframes floatY { 0%,100% { transform: translateY(0); } 50% { transform: translateY(-4px); } }
        @keyframes drop { 0% { transform: translateY(-4px); opacity: 0; } 50% { opacity: 1; } 100% { transform: translateY(10px); opacity: 0; } }
        .sun .rays { transform-origin: 50% 50%; animation: spinSlow 10s linear infinite; }

        .cloud { animation: floatY 4s ease-in-out infinite; }
        /* Premium contact form */
        #contact-modal .premium-card { background: #0b1220; border: 1px solid rgba(255,255,255,0.12); box-shadow: 0 20px 60px rgba(0,0,0,0.55), inset 0 0 0 1px rgba(255,255,255,0.04); }
        #contact-modal .bar { height: 3px; background: linear-gradient(90deg, #8b5cf6, #06b6d4, #10b981, #f59e0b); border-radius: 9999px; }
        .input-premium { background: rgba(17,24,39,0.9); border: 1px solid rgba(255,255,255,0.14); color: #fff; border-radius: 12px; padding: 10px 12px; }
        .input-premium::placeholder { color: rgba(255,255,255,0.6); }
        .input-premium:focus { outline: none; border-color: rgba(255,255,255,0.35); box-shadow: 0 0 0 4px rgba(99,102,241,0.15); }
        .btn-gradient { background: linear-gradient(90deg, #ef4444, #f59e0b); }
        .btn-gradient:hover { filter: brightness(1.05); }

        .raindrop { animation: drop 1s linear infinite; }
        .snow { animation: drop 1.6s linear infinite; }
        .bolt { animation: floatY 1.4s ease-in-out infinite; }
        /* Disable text selection sitewide (except form fields) */
        body { -webkit-user-select: none; -ms-user-select: none; user-select: none; }
        input, textarea, select { -webkit-user-select: text; -ms-user-select: text; user-select: text; }
        * { -webkit-tap-highlight-color: transparent; }
        /* Page fade-in */
        body.page-fade { opacity: 0; transition: opacity .6s ease; }
        body.page-fade.page-loaded { opacity: 1; }
        /* Circle cursor */
        .cursor-dot { position: fixed; top: 0; left: 0; width: 18px; height: 18px; border: 2px solid rgba(255,255,255,0.7); border-radius: 9999px; pointer-events: none; transform: translate(-50%, -50%); z-index: 10000; mix-blend-mode: difference; transition: transform .08s ease, box-shadow .2s, border-color .2s, background-color .2s; }
        .cursor-dot.click { box-shadow: 0 0 0 8px rgba(255,255,255,0.08); }
        @media (pointer: coarse) { .cursor-dot { display: none; } }

        /* Video background */
        .video-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: -2;
            object-fit: cover;
            pointer-events: none;
            opacity: 0;
            transition: opacity 1s ease-in-out;
        }
        .video-background.loaded {
            opacity: 1;
        }

        /* Copy button styles */
        .copy-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 4px 8px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-left: 8px;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }
        .copy-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            transform: translateY(-1px);
        }
        .copy-btn.copied {
            background: rgba(34, 197, 94, 0.2);
            border-color: rgba(34, 197, 94, 0.4);
            color: #22c55e;
        }

        /* Modern contact form */
        .contact-form-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .modern-input {
            background: white;
            border: 1px solid #d1d5db;
            color: #374151;
            font-size: 16px;
        }

        .modern-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .modern-input::placeholder {
            color: #9ca3af;
        }

        .outlined-button {
            background: transparent;
            border: 1px solid #000;
            color: #000;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .outlined-button:hover {
            background: #000;
            color: white;
            transform: translateY(-1px);
        }



        /* Noisy texture effect matching profile picture */
        .premium-form-container::after {
            content: '';
            position: absolute;
            top: -4px;
            left: -4px;
            right: -4px;
            bottom: -4px;
            border-radius: inherit;
            background-image:
                radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.08) 1px, transparent 1px),
                radial-gradient(circle at 50% 10%, rgba(99, 102, 241, 0.1) 1px, transparent 1px),
                radial-gradient(circle at 10% 90%, rgba(139, 92, 246, 0.08) 1px, transparent 1px),
                radial-gradient(circle at 90% 50%, rgba(6, 182, 212, 0.06) 1px, transparent 1px);
            background-size:
                20px 20px,
                30px 30px,
                25px 25px,
                35px 35px,
                40px 40px;
            background-position:
                0 0,
                10px 10px,
                5px 15px,
                15px 5px,
                20px 20px;
            opacity: 0.6;
            animation: noiseShift 15s ease-in-out infinite;
            z-index: -1;
        }

        @keyframes noiseShift {
            0%, 100% {
                transform: translate(0, 0) scale(1);
                opacity: 0.6;
            }
            25% {
                transform: translate(2px, -1px) scale(1.01);
                opacity: 0.4;
            }
            50% {
                transform: translate(-1px, 2px) scale(0.99);
                opacity: 0.7;
            }
            75% {
                transform: translate(1px, 1px) scale(1.005);
                opacity: 0.5;
            }
        }

        .holographic-border {
            position: relative;
            overflow: hidden;
            animation: formPulse 5s ease-in-out infinite;
        }
        .holographic-border::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.4), transparent);
            animation: holographicSweep 3s infinite;
        }
        @keyframes holographicSweep {
            0% { left: -100%; }
            100% { left: 100%; }
        }
        @keyframes formPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.01); }
        }

        .futuristic-input {
            background: rgba(15, 23, 42, 0.8);
            border: 1px solid rgba(99, 102, 241, 0.3);
            color: #fff;
            border-radius: 12px;
            padding: 12px 16px;
            font-size: 14px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            backdrop-filter: blur(10px);
        }
        .futuristic-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
            transition: all 0.3s ease;
        }
        .futuristic-input:focus {
            outline: none;
            border-color: rgba(99, 102, 241, 0.8);
            box-shadow:
                0 0 0 3px rgba(99, 102, 241, 0.15),
                0 0 25px rgba(99, 102, 241, 0.3),
                0 0 50px rgba(99, 102, 241, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            background: rgba(15, 23, 42, 0.95);
            transform: translateY(-2px);
        }
        .futuristic-input:focus::placeholder {
            color: rgba(255, 255, 255, 0.3);
            transform: translateX(4px);
        }

        /* Enhanced form field container */
        .form-field {
            position: relative;
            overflow: hidden;
        }
        .form-field::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
            transition: left 0.6s ease;
            border-radius: 12px;
            z-index: 0;
        }
        .form-field:hover::before {
            left: 100%;
        }

        /* Label enhancement */
        .premium-label {
            position: relative;
            display: inline-block;
            font-weight: 500;
            color: #e2e8f0;
            margin-bottom: 8px;
            transition: all 0.3s ease;
        }
        .premium-label::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, #6366f1, #8b5cf6);
            transition: width 0.3s ease;
            border-radius: 1px;
        }
        .form-field:focus-within .premium-label::after {
            width: 100%;
        }

        .quantum-button {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #06b6d4 100%);
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            color: white;
            font-weight: 600;
            font-size: 14px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(99, 102, 241, 0.4);
        }
        .quantum-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }
        .quantum-button:hover::before {
            left: 100%;
        }
        .quantum-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.6);
        }
        .quantum-button:active {
            transform: translateY(0);
        }

        .neural-grid {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                linear-gradient(rgba(99, 102, 241, 0.06) 1px, transparent 1px),
                linear-gradient(90deg, rgba(99, 102, 241, 0.06) 1px, transparent 1px),
                radial-gradient(circle at 30% 70%, rgba(139, 92, 246, 0.04) 2px, transparent 2px),
                radial-gradient(circle at 70% 30%, rgba(6, 182, 212, 0.03) 1px, transparent 1px);
            background-size:
                30px 30px,
                30px 30px,
                50px 50px,
                40px 40px;
            background-position:
                0 0,
                0 0,
                15px 25px,
                25px 15px;
            opacity: 0.3;
            pointer-events: none;
            border-radius: inherit;
            animation: gridNoiseShift 12s ease-in-out infinite;
        }
        @keyframes gridNoiseShift {
            0%, 100% {
                opacity: 0.3;
                transform: translate(0, 0);
            }
            25% {
                opacity: 0.2;
                transform: translate(1px, -1px);
            }
            50% {
                opacity: 0.4;
                transform: translate(-1px, 0px);
            }
            75% {
                opacity: 0.25;
                transform: translate(0px, 1px);
            }
        }

        /* Form content noise texture */
        .form-content-noise {
            position: relative;
        }
        .form-content-noise::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: inherit;
            background-image:
                radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.03) 1px, transparent 1px),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.02) 1px, transparent 1px),
                radial-gradient(circle at 40% 40%, rgba(99, 102, 241, 0.04) 1px, transparent 1px);
            background-size:
                15px 15px,
                25px 25px,
                20px 20px;
            background-position:
                0 0,
                7px 7px,
                3px 12px;
            opacity: 0.8;
            pointer-events: none;
            animation: contentNoiseShift 20s ease-in-out infinite;
            z-index: -1;
        }

        @keyframes contentNoiseShift {
            0%, 100% {
                transform: translate(0, 0);
                opacity: 0.8;
            }
            33% {
                transform: translate(1px, -0.5px);
                opacity: 0.6;
            }
            66% {
                transform: translate(-0.5px, 1px);
                opacity: 0.9;
            }
        }

    </style>
</head>
<body class="text-white page-fade">
    <!-- Video Background -->
    <video
        class="video-background"
        autoplay
        loop
        muted
        playsinline
        preload="auto"
    >
        <source src="bg.mp4" type="video/mp4">
    </video>

    <!-- Liquid Ether as fallback -->
    <div id="liquid-ether" class="liquid-ether-container" aria-hidden="true"></div>


    <!-- Top-left clock -->
    <div id="clock" class="fixed top-4 left-4 hud-badge text-white/90 rounded-xl px-3 py-1 text-sm font-mono select-none z-50">--:--:--</div>

    <!-- Top-right weather widget -->
    <div id="weather-widget" class="fixed top-4 right-4 flex flex-col items-end gap-2 z-50">
        <button id="weather-toggle" class="hud-badge rounded-xl px-3 py-2 flex items-center gap-2 text-white/90 hover:bg-white/10 transition">
            <span id="weather-icon" aria-hidden="true"></span>
            <span id="weather-temp" class="font-medium">--&deg;C</span>
            <i class="ri-arrow-down-s-line text-xl opacity-70"></i>
        </button>
        <div id="weather-bubble" class="bubble-card rounded-2xl p-4 w-80 hidden text-white/90">
            <div id="weather-current" class="flex items-center justify-between">
                <div>
                    <div class="text-sm opacity-80">Montreal</div>
                    <div id="weather-desc" class="text-lg font-semibold">--</div>
                </div>
                <div id="weather-icon-lg"></div>
            </div>
            <div class="mt-3 border-t border-white/10 pt-3">
                <div class="text-xs opacity-70 mb-2">Next 3 days</div>
                <div id="weather-forecast" class="grid grid-cols-3 gap-2"></div>
            </div>
        </div>
    </div>

    <!-- Circle cursor element -->
    <div id="cursor-dot" class="cursor-dot"></div>


    <div class="min-h-screen flex flex-col items-center justify-center px-4 py-8">
        <div class="w-full max-w-md mx-auto space-y-8">

            <div class="text-center fade-in" style="animation-delay: 0.2s;">
                <div class="profile-orb w-40 h-40 mx-auto mb-6 rounded-full overflow-hidden">
                    <img src="https://uzy.rf.gd/bg2.png" alt="YK" class="w-full h-full object-cover">
                </div>
                <div class="glass-morphism rounded-2xl p-6">
                    <h1 class="text-3xl font-bold mb-2">Yusuf</h1>
                    <div class="flex items-center justify-center text-gray-400 text-xs mt-2">
                        <span><EMAIL></span>
                        <button
                            class="copy-btn"
                            onclick="copyEmail()"
                            title="Copy @"
                        >
                            <i class="ri-file-copy-line"></i>
                        </button>
                    </div>
                </div>
            </div>

            <div class="space-y-4">
                <a href="https://www.instagram.com/uzygram/" target="_blank" rel="noopener noreferrer" class="social-button glass-card instagram-glow flex items-center w-full p-4 rounded-xl text-white no-underline fade-in" style="animation-delay: 0.4s;">
                    <div class="w-6 h-6 flex items-center justify-center mr-4">
                        <i class="ri-instagram-fill text-xl"></i>
                    </div>
                    <span class="font-medium">Instagram</span>
                </a>

                <a href="https://www.snapchat.com/@uzygram" target="_blank" rel="noopener noreferrer" class="social-button glass-card snapchat-glow flex items-center w-full p-4 rounded-xl text-white no-underline fade-in" style="animation-delay: 0.5s;">
                    <div class="w-6 h-6 flex items-center justify-center mr-4">
                        <i class="ri-snapchat-fill text-xl"></i>
                    </div>
                    <span class="font-medium">Snapchat</span>
                </a>

                <a href="https://www.tiktok.com/@uzymtl" target="_blank" rel="noopener noreferrer" class="social-button glass-card tiktok-glow flex items-center w-full p-4 rounded-xl text-white no-underline fade-in" style="animation-delay: 0.6s;">
                    <div class="w-6 h-6 flex items-center justify-center mr-4">
                        <i class="ri-tiktok-fill text-xl"></i>
                    </div>
                    <span class="font-medium">TikTok</span>
                </a>

                <a href="https://github.com/uzygram" target="_blank" rel="noopener noreferrer" class="social-button glass-card github-glow flex items-center w-full p-4 rounded-xl text-white no-underline fade-in" style="animation-delay: 0.7s;">
                    <div class="w-6 h-6 flex items-center justify-center mr-4">
                        <i class="ri-github-fill text-xl"></i>
                    </div>
                    <span class="font-medium">GitHub</span>
                </a>

                <a href="https://discord.com/users/ozility" target="_blank" rel="noopener noreferrer" class="social-button glass-card discord-glow flex items-center w-full p-4 rounded-xl text-white no-underline fade-in" style="animation-delay: 0.8s;">
                    <div class="w-6 h-6 flex items-center justify-center mr-4">
                        <i class="ri-discord-fill text-xl"></i>
                    </div>
                    <span class="font-medium">Discord</span>
                </a>

                <a href="https://open.spotify.com/user/12180822021" target="_blank" rel="noopener noreferrer" class="social-button glass-card spotify-glow flex items-center w-full p-4 rounded-xl text-white no-underline fade-in" style="animation-delay: 0.9s;">
                    <div class="w-6 h-6 flex items-center justify-center mr-4">
                        <i class="ri-spotify-fill text-xl"></i>
                    </div>
                    <span class="font-medium">Spotify</span>
                </a>

                <a href="https://yusuf.zone.id" target="_blank" rel="noopener noreferrer" class="social-button glass-card portfolio-glow flex items-center w-full p-4 rounded-xl text-white no-underline fade-in" style="animation-delay: 1.0s;">
                    <div class="w-6 h-6 flex items-center justify-center mr-4">
                        <i class="ri-earth-fill text-xl"></i>
                    </div>
                    <span class="font-medium">Portfolio</span>
                </a>

                <a href="#" id="contact-open" class="social-button glass-card contact-glow flex items-center w-full p-4 rounded-xl text-white no-underline fade-in" style="animation-delay: 1.1s;">
                    <div class="w-6 h-6 flex items-center justify-center mr-4">
                        <i class="ri-mail-fill text-xl"></i>
                    </div>
                    <span class="font-medium">Contact</span>
                </a>
            </div>
        </div>

        <footer class="mt-12 fade-in" style="animation-delay: 1.2s;">
            <div class="flex justify-center">
                <img src="yk.png" alt="YK" class="h-16 w-auto opacity-90 hover:opacity-100 transition" />
            </div>
        </footer>
    </div>
    <div id="contact-modal" class="fixed inset-0 z-50 hidden">
        <div class="absolute inset-0 bg-black/70 backdrop-blur-sm" id="contact-overlay"></div>
        <div class="relative mx-auto my-8 max-w-4xl w-[95%] flex items-center justify-center min-h-screen">
            <div class="contact-form-container bg-white rounded-xl shadow-lg relative overflow-hidden w-full max-w-4xl">
                <!-- Close button -->
                <button id="contact-close" class="absolute top-4 right-4 z-20 text-gray-600 hover:text-gray-800 transition-colors p-2 rounded-lg hover:bg-gray-100">
                    <i class="ri-close-line text-2xl"></i>
                </button>

                <!-- Two-column layout -->
                <div class="grid grid-cols-1 lg:grid-cols-2 min-h-[500px]">
                    <!-- Left Column - Form -->
                    <div class="p-8 lg:p-12 flex flex-col justify-center">
                        <h2 class="text-3xl font-bold text-gray-900 mb-8">Contact us</h2>

                        <!-- Contact Form -->
                        <form action="https://formcarry.com/s/S05h5ApUuLu" method="POST" class="space-y-6">
                            <div class="form-field">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                                <input
                                    type="email"
                                    name="email"
                                    required
                                    placeholder="Enter your email"
                                    class="modern-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                                >
                            </div>

                            <div class="form-field">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Name</label>
                                <input
                                    type="text"
                                    name="name"
                                    required
                                    placeholder="Enter your name"
                                    class="modern-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                                >
                            </div>

                            <div class="form-field">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Message</label>
                                <textarea
                                    name="message"
                                    rows="4"
                                    required
                                    placeholder="Enter your message"
                                    class="modern-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none"
                                ></textarea>
                            </div>

                            <button type="submit" class="outlined-button w-full py-3 px-6 border border-black text-black font-medium rounded-lg hover:bg-black hover:text-white transition-all duration-200">
                                SEND
                            </button>
                        </form>
                    </div>

                    <!-- Right Column - Image -->
                    <div class="bg-gray-50 flex items-center justify-center p-8">
                        <div class="w-full h-full flex items-center justify-center">
                            <img src="bg2.png" alt="Profile picture" class="max-w-full max-h-full object-contain rounded-lg">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <script id="button-interactions">
        // Copy email function
        function copyEmail() {
            const email = '<EMAIL>';
            const copyBtn = document.querySelector('.copy-btn');
            const copyText = copyBtn.querySelector('.copy-text');
            const copyIcon = copyBtn.querySelector('i');

            navigator.clipboard.writeText(email).then(() => {
                // Success feedback
                copyBtn.classList.add('copied');
                copyText.textContent = 'Copied!';
                copyIcon.className = 'ri-check-line';

                setTimeout(() => {
                    copyBtn.classList.remove('copied');
                    copyText.textContent = 'Copy';
                    copyIcon.className = 'ri-file-copy-line';
                }, 2000);
            }).catch(() => {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = email;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);

                copyBtn.classList.add('copied');
                copyText.textContent = 'Copied!';
                copyIcon.className = 'ri-check-line';

                setTimeout(() => {
                    copyBtn.classList.remove('copied');
                    copyText.textContent = 'Copy';
                    copyIcon.className = 'ri-file-copy-line';
                }, 2000);
            });
        }

        document.addEventListener('DOMContentLoaded', function() {
            const socialButtons = document.querySelectorAll('.social-button');

            socialButtons.forEach(button => {
                button.style.position = button.style.position || 'relative';
                button.addEventListener('click', function(e) {
                    const isContact = button.id === 'contact-open';
                    if (isContact) {
                        e.preventDefault();
                    }

                    const rect = button.getBoundingClientRect();
                    const ripple = document.createElement('div');
                    ripple.style.position = 'absolute';
                    ripple.style.borderRadius = '50%';
                    ripple.style.background = 'rgba(255, 255, 255, 0.3)';
                    ripple.style.transform = 'scale(0)';
                    ripple.style.animation = 'ripple 0.6s linear';
                    ripple.style.left = (e.clientX - rect.left) + 'px';
                    ripple.style.top = (e.clientY - rect.top) + 'px';
                    ripple.style.width = ripple.style.height = '20px';
                    ripple.style.marginLeft = ripple.style.marginTop = '-10px';

                    button.appendChild(ripple);

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });

            const style = document.createElement('style');
            style.textContent = `
                @keyframes ripple {
                    to {
                        transform: scale(4);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
        });
    </script>
    <script id="contact-modal-script">
        document.addEventListener('DOMContentLoaded', function() {
            const modal = document.getElementById('contact-modal');
            const openBtn = document.getElementById('contact-open');
            const closeBtn = document.getElementById('contact-close');
            const overlay = document.getElementById('contact-overlay');
            const form = modal ? modal.querySelector('form') : null;
            const submitBtn = form ? form.querySelector('button[type="submit"]') : null;

            function openModal(e) {
                if (e) e.preventDefault();
                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
                // Add entrance animation
                setTimeout(() => {
                    modal.querySelector('.premium-form-container').style.transform = 'scale(1)';
                    modal.querySelector('.premium-form-container').style.opacity = '1';
                }, 10);
            }

            function closeModal() {
                const container = modal.querySelector('.premium-form-container');
                container.style.transform = 'scale(0.95)';
                container.style.opacity = '0';
                setTimeout(() => {
                    modal.classList.add('hidden');
                    document.body.style.overflow = '';
                }, 200);
            }

            if (openBtn) openBtn.addEventListener('click', openModal);
            if (closeBtn) closeBtn.addEventListener('click', closeModal);
            if (overlay) overlay.addEventListener('click', closeModal);
            document.addEventListener('keydown', function(e) { if (e.key === 'Escape' && !modal.classList.contains('hidden')) closeModal(); });

            // Initialize modal container styles
            if (modal) {
                const container = modal.querySelector('.premium-form-container');
                container.style.transform = 'scale(0.95)';
                container.style.opacity = '0';
                container.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
            }

            if (form) {
                form.addEventListener('submit', async function(e) {
                    e.preventDefault();
                    if (!submitBtn) return form.submit();

                    submitBtn.disabled = true;
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<span class="relative z-10">Sending...</span>';
                    submitBtn.style.background = 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)';

                    try {
                        const res = await fetch(form.action, {
                            method: 'POST',
                            body: new FormData(form),
                            headers: { 'Accept': 'application/json' }
                        });

                        if (res.ok) {
                            submitBtn.innerHTML = '<span class="relative z-10">Message Sent ✓</span>';
                            submitBtn.style.background = 'linear-gradient(135deg, #10b981 0%, #059669 100%)';
                            form.reset();

                            setTimeout(() => {
                                submitBtn.innerHTML = originalText;
                                submitBtn.style.background = 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #06b6d4 100%)';
                                closeModal();
                            }, 2000);
                        } else {
                            throw new Error('Network response was not ok');
                        }
                    } catch (err) {
                        submitBtn.innerHTML = '<span class="relative z-10">Failed to Send - Retry</span>';
                        submitBtn.style.background = 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)';

                        setTimeout(() => {
                            submitBtn.innerHTML = originalText;
                            submitBtn.style.background = 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #06b6d4 100%)';
                        }, 3000);
                    } finally {
                        submitBtn.disabled = false;
                    }
                });
            }
        });
    </script>
    <script id="video-background">
        document.addEventListener('DOMContentLoaded', function() {
            const video = document.querySelector('.video-background');
            const liquidEther = document.getElementById('liquid-ether');

            if (video) {
                // Handle video loading and smooth fade-in
                video.addEventListener('loadeddata', function() {
                    // Video loaded successfully, fade it in
                    video.classList.add('loaded');

                    // After fade-in completes, hide liquid ether
                    setTimeout(() => {
                        if (liquidEther) {
                            liquidEther.style.opacity = '0';
                            liquidEther.style.transition = 'opacity 0.5s ease-out';
                            setTimeout(() => {
                                liquidEther.style.display = 'none';
                            }, 500);
                        }
                    }, 1000);
                });

                video.addEventListener('error', function() {
                    // Video failed to load, keep liquid ether as fallback
                    video.style.display = 'none';
                    console.log('Video failed to load, using Liquid Ether background');
                });

                // Handle video play events
                video.addEventListener('canplay', function() {
                    video.play().catch(function(error) {
                        console.log('Video autoplay failed:', error);
                        // If autoplay fails, still try to show the video but keep liquid ether
                        video.style.display = 'none';
                    });
                });

                // Additional event for when video actually starts playing
                video.addEventListener('playing', function() {
                    if (!video.classList.contains('loaded')) {
                        video.classList.add('loaded');

                        // Fade out liquid ether after video starts playing
                        setTimeout(() => {
                            if (liquidEther) {
                                liquidEther.style.opacity = '0';
                                liquidEther.style.transition = 'opacity 0.5s ease-out';
                                setTimeout(() => {
                                    liquidEther.style.display = 'none';
                                }, 500);
                            }
                        }, 500);
                    }
                });
            }
        });
    </script>

    <script id="clock-weather">
        document.addEventListener('DOMContentLoaded', function() {
            // Page fade-in
            document.body.classList.add('page-loaded');

            // Circle cursor follower
            const cursor = document.getElementById('cursor-dot');
            if (cursor) {
                let x=0, y=0, tx=0, ty=0;
                function loop(){ x += (tx - x)*0.25; y += (ty - y)*0.25; cursor.style.transform = `translate(${x}px, ${y}px)`; requestAnimationFrame(loop); }
                requestAnimationFrame(loop);
                window.addEventListener('mousemove', e=>{ tx = e.clientX; ty = e.clientY; });
                window.addEventListener('mousedown', ()=>{ cursor.classList.add('click'); setTimeout(()=>cursor.classList.remove('click'), 160); });
            }

            // Clock
            const clockEl = document.getElementById('clock');
            function pad(n){ return n.toString().padStart(2,'0'); }
            function updateClock(){
                const d = new Date();
                const h = pad(d.getHours()), m = pad(d.getMinutes()), s = pad(d.getSeconds());
                let tz = '';
                try {
                    const parts = new Intl.DateTimeFormat(undefined, { timeZoneName: 'short' }).formatToParts(d);
                    tz = parts.find(p => p.type === 'timeZoneName')?.value || '';
                } catch(e) {}
                if (clockEl) clockEl.textContent = `${h}:${m}:${s}`;
            }
            updateClock(); setInterval(updateClock, 1000);

            // Weather
            const API_KEY = window.OPENWEATHER_API_KEY || (document.querySelector('meta[name="openweather-api-key"]')?.content || '');
            const city = 'Montreal';
            const toggleBtn = document.getElementById('weather-toggle');
            const bubble = document.getElementById('weather-bubble');
            const iconSm = document.getElementById('weather-icon');
            const tempSm = document.getElementById('weather-temp');
            const currentDesc = document.getElementById('weather-desc');
            const iconLg = document.getElementById('weather-icon-lg');
            const forecastWrap = document.getElementById('weather-forecast');

            function mapIcon(main){
                const m = (main||'').toLowerCase();
                if (m.includes('thunder')) return 'thunder';
                if (m.includes('snow')) return 'snow';
                if (m.includes('rain')) return 'rain';
                if (m.includes('drizzle')) return 'rain';
                if (m.includes('cloud')) return 'clouds';
                if (m.includes('mist')||m.includes('fog')||m.includes('haze')) return 'mist';
                return 'clear';
            }
            function svgIcon(kind, size=32){
                const s = size; const half=s/2;
                if (kind==='clear') return `
                    <svg class="wx-icon sun" width="${s}" height="${s}" viewBox="0 0 32 32" fill="none">
                      <defs><radialGradient id="gSun" cx="50%" cy="50%"><stop offset="0%" stop-color="#fde68a"/><stop offset="100%" stop-color="#f59e0b"/></radialGradient></defs>
                      <circle cx="16" cy="16" r="6" fill="url(#gSun)"/>
                      <g class="rays" stroke="#fbbf24" stroke-width="2" stroke-linecap="round">
                        <line x1="16" y1="1" x2="16" y2="6"/>
                        <line x1="16" y1="26" x2="16" y2="31"/>
                        <line x1="1" y1="16" x2="6" y2="16"/>
                        <line x1="26" y1="16" x2="31" y2="16"/>
                        <line x1="5" y1="5" x2="8" y2="8"/>
                        <line x1="27" y1="27" x2="24" y2="24"/>
                        <line x1="5" y1="27" x2="8" y2="24"/>
                        <line x1="27" y1="5" x2="24" y2="8"/>
                      </g>
                    </svg>`;
                if (kind==='clouds') return `
                    <svg class="wx-icon cloud" width="${s}" height="${s}" viewBox="0 0 32 32" fill="none">
                      <g fill="#e5e7eb">
                        <ellipse cx="12" cy="18" rx="7" ry="5"/>
                        <ellipse cx="18" cy="16" rx="6" ry="5"/>
                        <ellipse cx="22" cy="19" rx="7" ry="5"/>
                      </g>
                    </svg>`;
                if (kind==='rain') return `
                    <svg class="wx-icon" width="${s}" height="${s}" viewBox="0 0 32 32" fill="none">
                      <g class="cloud" fill="#e5e7eb">
                        <ellipse cx="12" cy="12" rx="7" ry="5"/>
                        <ellipse cx="18" cy="10" rx="6" ry="5"/>
                        <ellipse cx="22" cy="13" rx="7" ry="5"/>
                      </g>
                      <g stroke="#60a5fa" stroke-width="2" stroke-linecap="round">
                        <line class="raindrop" x1="10" y1="18" x2="10" y2="24"/>
                        <line class="raindrop" x1="16" y1="19" x2="16" y2="25" style="animation-delay:.2s"/>
                        <line class="raindrop" x1="22" y1="18" x2="22" y2="24" style="animation-delay:.4s"/>
                      </g>
                    </svg>`;
                if (kind==='snow') return `
                    <svg class="wx-icon" width="${s}" height="${s}" viewBox="0 0 32 32" fill="none">
                      <g class="cloud" fill="#e5e7eb">
                        <ellipse cx="12" cy="12" rx="7" ry="5"/>
                        <ellipse cx="18" cy="10" rx="6" ry="5"/>
                        <ellipse cx="22" cy="13" rx="7" ry="5"/>
                      </g>
                      <g fill="#bfdbfe">
                        <circle class="snow" cx="12" cy="22" r="2" />
                        <circle class="snow" cx="18" cy="24" r="2" style="animation-delay:.3s"/>
                        <circle class="snow" cx="24" cy="22" r="2" style="animation-delay:.6s"/>
                      </g>
                    </svg>`;
                if (kind==='thunder') return `
                    <svg class="wx-icon" width="${s}" height="${s}" viewBox="0 0 32 32" fill="none">
                      <g class="cloud" fill="#e5e7eb">
                        <ellipse cx="12" cy="12" rx="7" ry="5"/>
                        <ellipse cx="18" cy="10" rx="6" ry="5"/>
                        <ellipse cx="22" cy="13" rx="7" ry="5"/>
                      </g>
                      <polygon class="bolt" points="16,18 12,26 18,23 16,30 22,21 17,23" fill="#fbbf24"/>
                    </svg>`;
                if (kind==='mist') return `
                    <svg class="wx-icon" width="${s}" height="${s}" viewBox="0 0 32 32" fill="none">
                      <g stroke="#cbd5e1" stroke-width="2" stroke-linecap="round">
                        <line x1="6" y1="14" x2="26" y2="14"/>
                        <line x1="4" y1="18" x2="24" y2="18"/>
                        <line x1="8" y1="22" x2="28" y2="22"/>
                      </g>
                    </svg>`;
                return svgIcon('clear', size);
            }

            function dayShort(ts){
                return new Date(ts*1000).toLocaleDateString(undefined,{ weekday:'short' });
            }

            async function fetchWeather(){
                if (!API_KEY) {
                    if (currentDesc) currentDesc.textContent = 'Add API key';
                    return;
                }
                try{
                    const [curRes, fcRes] = await Promise.all([
                        fetch(`https://api.openweathermap.org/data/2.5/weather?q=${encodeURIComponent(city)}&units=metric&appid=${API_KEY}`),
                        fetch(`https://api.openweathermap.org/data/2.5/forecast?q=${encodeURIComponent(city)}&units=metric&appid=${API_KEY}`)
                    ]);
                    if (!curRes.ok || !fcRes.ok) throw new Error('Weather fetch failed');
                    const cur = await curRes.json();
                    const fc = await fcRes.json();

                    const t = Math.round(cur.main?.temp ?? 0);
                    const main = cur.weather?.[0]?.main || '';
                    const desc = cur.weather?.[0]?.description || '';
                    const kind = mapIcon(main);

                    if (tempSm) tempSm.textContent = `${t}°C`;
                    if (iconSm) iconSm.innerHTML = svgIcon(kind, 24);
                    if (iconLg) iconLg.innerHTML = svgIcon(kind, 48);
                    if (currentDesc) currentDesc.textContent = desc.charAt(0).toUpperCase() + desc.slice(1);

                    // Build 3-day forecast from 3-hourly data
                    const todayStr = new Date().toISOString().slice(0,10);
                    const byDate = new Map();
                    for (const item of (fc.list||[])){
                        const dateStr = item.dt_txt.slice(0,10);
                        if (dateStr === todayStr) continue; // skip today
                        if (!byDate.has(dateStr)) byDate.set(dateStr, []);
                        byDate.get(dateStr).push(item);
                    }
                    const next3 = Array.from(byDate.keys()).slice(0,3);
                    if (forecastWrap) forecastWrap.innerHTML = '';
                    for (const dateStr of next3){
                        const items = byDate.get(dateStr)||[];
                        // choose around midday (12:00)
                        let pick = items.find(x=>x.dt_txt.includes('12:00:00')) || items[Math.floor(items.length/2)] || items[0];
                        const temp = Math.round(pick.main?.temp ?? 0);
                        const pKind = mapIcon(pick.weather?.[0]?.main||'');
                        const dayTs = Math.floor(new Date(dateStr+'T00:00:00').getTime()/1000);
                        const cell = document.createElement('div');
                        cell.className = 'glass-card rounded-xl p-2 text-center text-sm';
                        cell.innerHTML = `
                            <div class="opacity-80 text-xs">${dayShort(dayTs)}</div>
                            <div class="flex items-center justify-center my-1">${svgIcon(pKind, 28)}</div>
                            <div class="font-semibold">${temp}°C</div>`;
                        if (forecastWrap) forecastWrap.appendChild(cell);
                    }
                } catch(e){
                    if (currentDesc) currentDesc.textContent = 'Weather unavailable';
                }
            }

            if (toggleBtn && bubble){
                // Init ARIA
                toggleBtn.setAttribute('aria-controls','weather-bubble');
                toggleBtn.setAttribute('aria-expanded','false');

                let outsideHandler = null;
                let escHandler = null;

                function openBubble(){
                    bubble.classList.remove('hidden');
                    // force reflow so transition triggers
                    void bubble.offsetWidth;
                    bubble.classList.add('open');
                    toggleBtn.setAttribute('aria-expanded','true');

                    outsideHandler = (e)=>{
                        const widget = document.getElementById('weather-widget');
                        if (widget && !widget.contains(e.target)) closeBubble();
                    };
                    escHandler = (e)=>{ if (e.key === 'Escape') closeBubble(); };
                    document.addEventListener('click', outsideHandler);
                    document.addEventListener('keydown', escHandler);
                }
                function closeBubble(){
                    bubble.classList.remove('open');
                    toggleBtn.setAttribute('aria-expanded','false');
                    const onEnd = ()=>{ bubble.classList.add('hidden'); bubble.removeEventListener('transitionend', onEnd); };
                    bubble.addEventListener('transitionend', onEnd);
                    if (outsideHandler) document.removeEventListener('click', outsideHandler);
                    if (escHandler) document.removeEventListener('keydown', escHandler);
                    outsideHandler = escHandler = null;
                }

                toggleBtn.addEventListener('click', (e)=>{
                    e.stopPropagation();
                    if (bubble.classList.contains('open')) closeBubble(); else openBubble();
                });
            }

            fetchWeather();
            setInterval(fetchWeather, 10*60*1000); // refresh every 10 minutes


        });
    </script>



<script src="https://unpkg.com/three@0.158.0/build/three.min.js"></script>
<script src="script.js"></script>
</body>
</html>




